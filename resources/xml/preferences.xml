<model.Preference>
  <boxID>19352</boxID>
  <playerWidth>9600</playerWidth>
  <playerHeight>1080</playerHeight>
  <playerLeft>0</playerLeft>
  <playerTop>0</playerTop>
  <scale>1.0</scale>
  <updateCheckerFrequency>60</updateCheckerFrequency>
  <ftpTransfer>true</ftpTransfer>
  <debugEnabled>true</debugEnabled>
  <clearContentOnStartup>false</clearContentOnStartup>
  <resendContentOnStartup>false</resendContentOnStartup>
  <checkUpdates>true</checkUpdates>
  <checkLogging>true</checkLogging>
  <eheckEmailSending>false</eheckEmailSending>
  <sendPlaylistRecord>false</sendPlaylistRecord>
  <uploadLogs>false</uploadLogs>
  <zeroPauseTime>false</zeroPauseTime>
  <transitionEnabled>true</transitionEnabled>
  <numDaysToKeepLog>0</numDaysToKeepLog>
  <rootPassword>street</rootPassword>
  <mediaPlayer>12</mediaPlayer>
  <memorySize>512</memorySize>
  <scheduleTime>03:00:00</scheduleTime>
  <scheduleday>1</scheduleday>
  <playerUpgraded>true</playerUpgraded>
  <countdownTimerTune>0</countdownTimerTune>
  <firstCyclePauseTime>200</firstCyclePauseTime>
  <scheduleHour>6</scheduleHour>
  <scheduleMin>3</scheduleMin>
  <scheduleUpdateHour>6</scheduleUpdateHour>
  <scheduleUpdateMin>21</scheduleUpdateMin>
  <logLevel>2</logLevel>
  <maxVideoWidth>1280</maxVideoWidth>
  <maxVideoHeight>720</maxVideoHeight>
  <boxWidth>0</boxWidth>
  <boxHeight>0</boxHeight>
  <wmpTune>0</wmpTune>
  <wmpTuneTime>5400</wmpTuneTime>
  <isLoadImageFromDist>false</isLoadImageFromDist>
  <version>13.20 </version>
  <osbit>32 Bit</osbit>
  <webBrowserType>0</webBrowserType>
  <isReboot>false</isReboot>
  <isUpdate>true</isUpdate>
  <isPreLoad>false</isPreLoad>
  <isMultiPList>false</isMultiPList>
</model.Preference>