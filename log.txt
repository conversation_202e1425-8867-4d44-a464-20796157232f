03:29:38.801 WARN  main | FileNotFound resources\xml\preferences.xml
03:29:38.803 INFO  main | Log Level: DEBUG
03:29:38.804 INFO  main | Player started on WINDOWS, v13.18 32 Bit
03:29:38.867 INFO  main | Start of Thread BoxInfoSynchronizer in player.Test.runPlayer().
03:29:38.868 INFO  main | Preference [boxID=0, playerWidth=1080, playerHeight=1080, playerLeft=0, playerTop=0, scale=1.0, updateCheckerFrequency=60, resendContentOnStartup=false, checkUpdates=true, sendPlaylistRecord=false, zeroPauseTime=false, scheduleHour=6, scheduleMin=3, mediaPlayer=10, memorySize=512, playerUpgraded=true, countdownTimerTune=0, firstCyclePauseTime=200, logLevel=2, maxVideoWidth=1280, maxVideoHeight=720, boxWidth=0, boxHeight=0, wmpTune=0, wmpTuneTime=0, isLoadImageFromDist=false, version=null, versionToUpgrade=null]
03:29:38.870 INFO  BoxInfoSynchronizer | Get Hardware Info: 
03:29:38.874 INFO  main | Log Level: DEBUG
03:29:39.039 INFO  main | Add Windows VLCLIB path
03:29:39.040 WARN  main | FileNotFound resources\backup\playlists.xml
03:29:39.040 ERROR main | File loading from resources\backup\playlists.xml is failed
03:29:39.040 INFO  main | loadContents: 0
03:29:39.041 INFO  main | loadContainers: 0
03:29:39.041 INFO  main | Now Time: Fri Jul 11 03:29:39 EDT 2025. BoxID: 0
03:29:39.043 INFO  main | Resetting inventory def...
03:29:39.044 INFO  main | Try to delete non existing file: resources\csv\inventory_definition.csv
03:29:39.044 DEBUG main | updateInventoryItemsWithNewData 0 / 0
03:29:39.044 INFO  main | Try to delete non existing file: resources\csv\inventory.csv
03:29:39.044 INFO  main | createInventoryRecords: 0 / 0
03:29:39.074 INFO  BoxInfoSynchronizer | Encode: 13.18
03:29:39.075 INFO  BoxInfoSynchronizer | Encode: Windows 10
03:29:39.075 INFO  BoxInfoSynchronizer | Encode: 10.0
03:29:39.075 INFO  BoxInfoSynchronizer | Encode: 1.8.0_202
03:29:39.075 INFO  BoxInfoSynchronizer | Encode: 0
03:29:39.077 INFO  BoxInfoSynchronizer | Encode: {"hwinfo":""}
03:29:39.077 INFO  BoxInfoSynchronizer | Encode: 512
03:29:39.078 INFO  BoxInfoSynchronizer | Encode: 03:00:00
03:29:39.078 INFO  BoxInfoSynchronizer | Encode: 1
03:29:39.078 INFO  BoxInfoSynchronizer | Encode: 8C-3B-4A-36-17-6D
03:29:39.271 INFO  BoxInfoSynchronizer | [Fri Jul 11 03:29:39 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=0&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=playerInfo&version=13.18&os=Windows+10&osversion=10.0&java_version=1.8.0_202&teamviewer_id=0&hwinfo=%7B%22hwinfo%22%3A%22%22%7D&memory=512&restart_schedule_time=03%3A00%3A00&restart_schedule_day=1&mac_address=8C-3B-4A-36-17-6D
03:29:39.672 INFO  BoxInfoSynchronizer | POST: 200OK Unable to Verify Identity
03:29:39.712 INFO  main | get box size: 0, 0
03:29:39.712 INFO  main | get max size from containers: 0, 0
03:29:39.714 INFO  main | saveObjectToXMLFile: resources\xml\preferences.xml
03:29:39.850 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: true
03:29:39.852 INFO  Player | Get null feature definition
03:29:39.852 INFO  Player | Playlist queue is empty.
03:29:39.854 INFO  Updater | Updater Thread Start
03:29:39.854 INFO  main | 5 20 1
03:29:39.859 INFO  main | waiting time: 6660000
03:29:39.961 INFO  main | saveObjectToXMLFile: resources\xml\preferences.xml
03:29:39.965 INFO  main | set default restart time
03:29:39.968 INFO  main | ScreenConnectionThread Started With 5 Screens ON.
03:29:40.038 INFO  TimeSync | [Fri Jul 11 03:29:40 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=0&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=TimeSync&boxdate=2025-07-11 03:29:39
03:29:40.079 INFO  TimeSync | POST: 200OK Unable to Verify Identity
03:29:44.854 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:29:44.854 INFO  Player | Playlist queue is empty.
03:29:49.856 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:29:49.856 INFO  Player | Playlist queue is empty.
03:29:50.039 INFO  Updater | [Fri Jul 11 03:29:50 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=0&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:29:50.084 INFO  Updater | POST: 200OK Unable to Verify Identity
03:29:54.858 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:29:54.858 INFO  Player | Playlist queue is empty.
03:29:59.861 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:29:59.861 INFO  Player | Playlist queue is empty.
03:30:04.863 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:04.863 INFO  Player | Playlist queue is empty.
03:30:09.864 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:09.864 INFO  Player | Playlist queue is empty.
03:30:14.867 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:14.867 INFO  Player | Playlist queue is empty.
03:30:19.870 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:19.870 INFO  Player | Playlist queue is empty.
03:30:24.872 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:24.872 INFO  Player | Playlist queue is empty.
03:30:26.265 INFO  Updater | [Fri Jul 11 03:30:26 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=0&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:30:26.308 INFO  Updater | POST: 200OK Unable to Verify Identity
03:30:29.875 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:29.875 INFO  Player | Playlist queue is empty.
03:30:34.876 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:34.876 INFO  Player | Playlist queue is empty.
03:30:35.589 INFO  saveChanges | Log Level: DEBUG
03:30:35.589 INFO  AdminFrame_ResendContent | Resend Content From Player
03:30:35.591 INFO  saveChanges | 5 20 1
03:30:35.597 INFO  saveChanges | waiting time: 6600000
03:30:35.602 INFO  saveChanges | saveObjectToXMLFile: resources\xml\preferences.xml
03:30:35.609 INFO  saveChanges | SaveChanges done. isUpdateViews: true
03:30:35.612 INFO  BoxInfoSynchronizer | Get Hardware Info: 
03:30:35.784 INFO  AdminFrame_ResendContent | [Fri Jul 11 03:30:35 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=resendcontent
03:30:35.795 INFO  BoxInfoSynchronizer | Encode: 13.18
03:30:35.795 INFO  BoxInfoSynchronizer | Encode: Windows 10
03:30:35.796 INFO  BoxInfoSynchronizer | Encode: 10.0
03:30:35.797 INFO  BoxInfoSynchronizer | Encode: 1.8.0_202
03:30:35.797 INFO  BoxInfoSynchronizer | Encode: 0
03:30:35.797 INFO  BoxInfoSynchronizer | Encode: {"hwinfo":""}
03:30:35.798 INFO  BoxInfoSynchronizer | Encode: 512
03:30:35.798 INFO  BoxInfoSynchronizer | Encode: 03:00:00
03:30:35.798 INFO  BoxInfoSynchronizer | Encode: 1
03:30:35.799 INFO  BoxInfoSynchronizer | Encode: 8C-3B-4A-36-17-6D
03:30:35.970 INFO  BoxInfoSynchronizer | [Fri Jul 11 03:30:35 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=playerInfo&version=13.18&os=Windows+10&osversion=10.0&java_version=1.8.0_202&teamviewer_id=0&hwinfo=%7B%22hwinfo%22%3A%22%22%7D&memory=512&restart_schedule_time=03%3A00%3A00&restart_schedule_day=1&mac_address=8C-3B-4A-36-17-6D
03:30:36.044 INFO  BoxInfoSynchronizer | POST: 200OK 
03:30:36.866 INFO  AdminFrame_ResendContent | POST: 200OK {"data":{"success":true},"result":"success","timespan":"956.4211ms"}
03:30:39.882 INFO  Player | saveObjectToXMLFile: resources\xml\preferences.xml
03:30:39.886 INFO  Player | clearContentForContainers. Del/Update: true/true
03:30:39.887 WARN  Player | FileNotFound resources\backup\playlists.xml
03:30:39.887 ERROR Player | File loading from resources\backup\playlists.xml is failed
03:30:39.888 INFO  Player | loadContents: 0
03:30:39.888 INFO  Player | loadContainers: 0
03:30:39.888 INFO  Player | Try to delete non existing file: resources\csv\inventory_definition.csv
03:30:39.889 DEBUG Player | updateInventoryItemsWithNewData 0 / 0
03:30:39.889 INFO  Player | Try to delete non existing file: resources\csv\inventory.csv
03:30:39.889 INFO  Player | createInventoryRecords: 0 / 0
03:30:39.890 INFO  Player | Get null feature definition
03:30:39.891 INFO  Player | saveObjectToXMLFile: resources\xml\inventory_def.xml
03:30:39.893 INFO  Player | saveObjectToXMLFile: resources\xml\inventory.xml
03:30:39.895 INFO  Player | clean up containerviews
03:30:39.896 INFO  Player | get box size: 0, 0
03:30:39.896 INFO  Player | get max size from containers: 0, 0
03:30:39.897 DEBUG Player | PageLayout Updated
03:30:39.897 INFO  Player | Now Time: Fri Jul 11 03:30:39 EDT 2025. BoxID: 19352
03:30:39.900 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: true
03:30:39.900 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:39.903 INFO  Player | Playlist queue is empty.
03:30:40.069 INFO  PlayerUpdateConfirmation | [Fri Jul 11 03:30:40 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=ContentUpdateConfirmation&jsonparameter={}
03:30:40.118 INFO  PlayerUpdateConfirmation | POST: 200OK Json parameter is null
03:30:44.904 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:44.904 INFO  Player | Playlist queue is empty.
03:30:49.907 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:49.907 INFO  Player | Playlist queue is empty.
03:30:54.908 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:54.908 INFO  Player | Playlist queue is empty.
03:30:59.856 INFO  Monitor | Mem KB(max:free): 506816 : 456129
03:30:59.910 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:30:59.910 INFO  Player | Playlist queue is empty.
03:31:02.491 INFO  Updater | [Fri Jul 11 03:31:02 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:31:02.556 INFO  Updater | POST: 200OK false
03:31:04.912 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:04.912 INFO  Player | Playlist queue is empty.
03:31:09.913 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:09.913 INFO  Player | Playlist queue is empty.
03:31:14.915 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:14.915 INFO  Player | Playlist queue is empty.
03:31:19.916 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:19.916 INFO  Player | Playlist queue is empty.
03:31:24.918 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:24.918 INFO  Player | Playlist queue is empty.
03:31:29.920 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:29.920 INFO  Player | Playlist queue is empty.
03:31:34.921 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:34.921 INFO  Player | Playlist queue is empty.
03:31:38.734 INFO  Updater | [Fri Jul 11 03:31:38 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:31:38.793 INFO  Updater | POST: 200OK true
03:31:38.794 INFO  Updater | Downloading: https://www.digitalmarketingbox.com/webtool/boxdata/19352/resources.zip -> resources.zip
03:31:38.832 INFO  Updater | Downloaded and Saved a file: resources.zip, len: 1895
03:31:39.012 INFO  Updater | [Fri Jul 11 03:31:39 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=completeupdate
03:31:39.072 INFO  Updater | POST: 200OK 
03:31:39.073 INFO  Updater | Extracting file: .\.\resources\xml\commands.xml
03:31:39.075 INFO  Updater | Extracting file: .\.\resources\csv\container.csv
03:31:39.076 INFO  Updater | Extracting file: .\.\resources\csv\playlist.csv
03:31:39.077 INFO  Updater | Extracting file: .\.\resources\csv\playlist_record.csv
03:31:39.077 INFO  Updater | Extracting file: .\.\resources\csv\content.csv
03:31:39.078 INFO  Updater | downloadContents: Get a content.csv
03:31:39.080 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.080 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.080 INFO  Updater | [Content] id: 2133589, path: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4, hasUpdate: true
03:31:39.081 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.081 INFO  Updater | [Content] id: 2133784, path: https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4, hasUpdate: true
03:31:39.081 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.082 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.083 INFO  Updater | [Content] id: 2133549, path: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-EN-1920x1080.mp4, hasUpdate: true
03:31:39.084 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.084 INFO  Updater | [Content] id: 2133589, path: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4, hasUpdate: true
03:31:39.085 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:39.085 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:39.085 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.086 INFO  Updater | [Content] id: 2133784, path: https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4, hasUpdate: true
03:31:39.086 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:39.086 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:39.087 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.087 INFO  Updater | [Content] id: 2131620, path: https://images.unoapp.com/boxdata/asset89122/images/WOK-CNY2025-1920x1080-FR_SR.mp4, hasUpdate: true
03:31:39.088 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:39.088 INFO  Updater | [Content] id: 2130547, path: https://images.unoapp.com/boxdata/asset88393/images/MW-HolidayGiftCard2024-1920x1080-EN.mp4, hasUpdate: true
03:31:39.088 INFO  Updater | [Content] id: 2133916, path: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, hasUpdate: true
03:31:39.089 INFO  Updater | [Content] id: 2133916, path: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, hasUpdate: true
03:31:39.089 INFO  Updater | [Content] id: 2133916, path: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, hasUpdate: true
03:31:39.089 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:39.090 INFO  Updater | [Content] id: 2133918, path: https://images.unoapp.com/boxdata/asset89534/images/MicrosoftTeams-video.mp4, hasUpdate: true
03:31:39.090 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset88393/images/MW-HolidayGiftCard2024-1920x1080-EN.mp4 -> resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4.TEMP
03:31:39.598 INFO  Updater | Downloaded and Saved a file: resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4.TEMP, len: 5322022
03:31:39.599 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89122/images/WOK-CNY2025-1920x1080-FR_SR.mp4 -> resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4.TEMP
03:31:39.923 INFO  Player | Total number of record: 0, playlist size: 0, isFirstTime: false
03:31:39.926 INFO  Player | Playlist queue is empty.
03:31:40.088 INFO  Updater | Downloaded and Saved a file: resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4.TEMP, len: 5321723
03:31:40.088 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4 -> resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4.TEMP
03:31:40.308 INFO  Updater | Downloaded and Saved a file: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4.TEMP, len: 2507142
03:31:40.308 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4 -> resources\videos\SeniorMondays-1920x1080-EN-SR.mp4.TEMP
03:31:40.578 INFO  Updater | Downloaded and Saved a file: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4.TEMP, len: 3100578
03:31:40.578 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg -> resources\images\pexels-souvenirpixel..._imresizer.jpeg
03:31:40.626 INFO  Updater | Downloaded and Saved a file: resources\images\pexels-souvenirpixel..._imresizer.jpeg, len: 339474
03:31:40.626 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg -> resources\images\pexels-souvenirpixel..._imresizer(1).jpeg
03:31:40.665 INFO  Updater | Downloaded and Saved a file: resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, len: 339474
03:31:40.665 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 -> resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4.TEMP
03:31:40.846 INFO  Updater | Downloaded and Saved a file: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4.TEMP, len: 2048721
03:31:40.846 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-EN-1920x1080.mp4 -> resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4.TEMP
03:31:41.090 INFO  Updater | Downloaded and Saved a file: resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4.TEMP, len: 2513282
03:31:41.090 INFO  Updater | Downloading: https://images.unoapp.com/boxdata/asset89534/images/MicrosoftTeams-video.mp4 -> resources\videos\MicrosoftTeams-video.mp4.TEMP
03:31:41.232 INFO  Updater | Downloaded and Saved a file: resources\videos\MicrosoftTeams-video.mp4.TEMP, len: 1599949
03:31:41.247 INFO  Updater | Deleted file: resources\xml\commands.xml
03:31:41.247 INFO  Updater | executeCmd: RESET_PLAYER
03:31:41.250 INFO  Updater | saveObjectToXMLFile: resources\xml\preferences.xml
03:31:41.253 INFO  Updater | loadContents: 0
03:31:41.253 INFO  Updater | loadContainers: 0
03:31:41.254 WARN  Updater | FileNotFound resources\backup\playlists.xml
03:31:41.255 ERROR Updater | File loading from resources\backup\playlists.xml is failed
03:31:41.256 INFO  Updater | [Playlist] id: 180522, name: Default, order: 1, active: true
03:31:41.257 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.257 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.257 INFO  Updater | [Content] id: 2133589, path: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4, hasUpdate: true
03:31:41.258 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.259 INFO  Updater | [Content] id: 2133784, path: https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4, hasUpdate: true
03:31:41.259 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.259 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.260 INFO  Updater | [Content] id: 2133549, path: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-EN-1920x1080.mp4, hasUpdate: true
03:31:41.261 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.261 INFO  Updater | [Content] id: 2133589, path: https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4, hasUpdate: true
03:31:41.262 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:41.262 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:41.263 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.263 INFO  Updater | [Content] id: 2133784, path: https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4, hasUpdate: true
03:31:41.263 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:41.264 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:41.264 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.264 INFO  Updater | [Content] id: 2131620, path: https://images.unoapp.com/boxdata/asset89122/images/WOK-CNY2025-1920x1080-FR_SR.mp4, hasUpdate: true
03:31:41.265 INFO  Updater | [Content] id: 2133913, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg, hasUpdate: true
03:31:41.265 INFO  Updater | [Content] id: 2130547, path: https://images.unoapp.com/boxdata/asset88393/images/MW-HolidayGiftCard2024-1920x1080-EN.mp4, hasUpdate: true
03:31:41.266 INFO  Updater | [Content] id: 2133916, path: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, hasUpdate: true
03:31:41.266 INFO  Updater | [Content] id: 2133916, path: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, hasUpdate: true
03:31:41.266 INFO  Updater | [Content] id: 2133916, path: https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, hasUpdate: true
03:31:41.267 INFO  Updater | [Content] id: 2133914, path: https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg, hasUpdate: true
03:31:41.269 INFO  Updater | [Content] id: 2133918, path: https://images.unoapp.com/boxdata/asset89534/images/MicrosoftTeams-video.mp4, hasUpdate: true
03:31:41.271 INFO  Updater | [Container] id: 363380, name: FS, l/t/w/h: 0/0/9600/1080, depth: 1, type: All
03:31:41.273 INFO  Updater | [Container] id: 363379, name: S, l/t/w/h: 0/0/9600/1080, depth: 2, type: All
03:31:41.273 INFO  Updater | [Container] id: 363385, name: E, l/t/w/h: 0/0/1920/1080, depth: 3, type: All
03:31:41.274 INFO  Updater | [Container] id: 363384, name: D, l/t/w/h: 1920/0/1920/1080, depth: 4, type: All
03:31:41.274 INFO  Updater | [Container] id: 363383, name: C, l/t/w/h: 3840/0/1920/1080, depth: 5, type: All
03:31:41.276 INFO  Updater | [Container] id: 363382, name: B, l/t/w/h: 5760/0/1920/1080, depth: 6, type: All
03:31:41.276 INFO  Updater | [Container] id: 363381, name: A, l/t/w/h: 7680/0/1920/1080, depth: 7, type: All
03:31:41.279 INFO  Updater | [Record] ID: 1811271, playlistId: 180522, containerId: 363381, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2030-01-01, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.281 INFO  Updater | [Record] ID: 1811339, playlistId: 180522, containerId: 363385, contentID: 2133916, pauseTime: 5, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.284 INFO  Updater | [Record] ID: 1811272, playlistId: 180522, containerId: 363383, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.285 INFO  Updater | [Record] ID: 1811275, playlistId: 180522, containerId: 363382, contentID: 2133589, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.287 INFO  Updater | [Record] ID: 1811276, playlistId: 180522, containerId: 363382, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.288 INFO  Updater | [Record] ID: 1811277, playlistId: 180522, containerId: 363384, contentID: 2133784, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.290 INFO  Updater | [Record] ID: 1811278, playlistId: 180522, containerId: 363382, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.291 INFO  Updater | [Record] ID: 1811280, playlistId: 180522, containerId: 363384, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.294 INFO  Updater | [Record] ID: 1811281, playlistId: 180522, containerId: 363381, contentID: 2133549, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.296 INFO  Updater | [Record] ID: 1811282, playlistId: 180522, containerId: 363381, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.297 INFO  Updater | [Record] ID: 1811340, playlistId: 180522, containerId: 363385, contentID: 2133914, pauseTime: 5, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.299 INFO  Updater | [Record] ID: 1811284, playlistId: 180522, containerId: 363381, contentID: 2133914, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.300 INFO  Updater | [Record] ID: 1811285, playlistId: 180522, containerId: 363383, contentID: 2133914, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.303 INFO  Updater | [Record] ID: 1811283, playlistId: 180522, containerId: 363384, contentID: 2133589, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.305 INFO  Updater | [Record] ID: 1811286, playlistId: 180522, containerId: 363384, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.307 INFO  Updater | [Record] ID: 1811338, playlistId: 180522, containerId: 363381, contentID: 2133916, pauseTime: 5, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.309 INFO  Updater | [Record] ID: 1811287, playlistId: 180522, containerId: 363382, contentID: 2133784, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.310 INFO  Updater | [Record] ID: 1811342, playlistId: 180522, containerId: 363385, contentID: 2133918, pauseTime: 40, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.313 INFO  Updater | [Record] ID: 1811288, playlistId: 180522, containerId: 363382, contentID: 2133914, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.315 INFO  Updater | [Record] ID: 1811295, playlistId: 180522, containerId: 363384, contentID: 2130547, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.316 INFO  Updater | [Record] ID: 1811290, playlistId: 180522, containerId: 363382, contentID: 2133914, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.318 INFO  Updater | [Record] ID: 1811337, playlistId: 180522, containerId: 363383, contentID: 2133916, pauseTime: 5, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.319 INFO  Updater | [Record] ID: 1811292, playlistId: 180522, containerId: 363384, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.321 INFO  Updater | [Record] ID: 1811293, playlistId: 180522, containerId: 363381, contentID: 2131620, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.324 INFO  Updater | [Record] ID: 1811294, playlistId: 180522, containerId: 363381, contentID: 2133913, pauseTime: 0, transition: -1, clear: false, From/ToDate: 2025-07-09/2070-12-31, From/ToTime: 00:00:00/23:59:00, days: Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday, playmethod: settime
03:31:41.325 INFO  Updater | Saving XML files..
03:31:41.325 INFO  Updater | Unable to backup file: resources\xml\containers.xml
03:31:41.325 INFO  Updater | Unable to backup file: resources\xml\contents.xml
03:31:41.326 INFO  Updater | Unable to backup file: resources\xml\playlists.xml
03:31:41.326 INFO  Updater | saveObjectToXMLFile: resources\xml\containers.xml
03:31:41.329 INFO  Updater | saveObjectToXMLFile: resources\xml\contents.xml
03:31:41.331 INFO  Updater | saveObjectToXMLFile: resources\xml\playlists.xml
03:31:41.350 INFO  Updater | Deleted file: resources\csv\container.csv
03:31:41.350 INFO  Updater | Deleted file: resources\csv\content.csv
03:31:41.351 INFO  Updater | Deleted file: resources\csv\playlist.csv
03:31:41.352 INFO  Updater | Deleted file: resources\csv\playlist_record.csv
03:31:41.353 INFO  Updater | Try to delete non existing file: resources\csv\page.csv
03:31:41.418 INFO  Thread-19 | [Fri Jul 11 03:31:41 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=ContentUpdateConfirmation&jsonparameter={"contents":[2130547,2131620,2133589,2133784,2133913,2133914,2133916,2133549,2133918]}
03:31:41.590 INFO  Thread-19 | POST: 200OK 
03:31:44.930 INFO  Player | clearContentForContainers. Del/Update: false/true
03:31:44.931 INFO  Player | moved a video file: f08e5e77-9db2-4b83-a1d4-05f680312451.mp4.TEMP -> f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:44.932 INFO  Player | moved a video file: LTO-FTM-JULY2025-EN-1920x1080.mp4.TEMP -> LTO-FTM-JULY2025-EN-1920x1080.mp4
03:31:44.934 INFO  Player | moved a video file: LTO-FTM-JULY2025-FR-1920x1080.mp4.TEMP -> LTO-FTM-JULY2025-FR-1920x1080.mp4
03:31:44.934 INFO  Player | moved a video file: MicrosoftTeams-video.mp4.TEMP -> MicrosoftTeams-video.mp4
03:31:44.935 INFO  Player | moved a video file: MW-HolidayGiftCard2024-1920x1080-EN.mp4.TEMP -> MW-HolidayGiftCard2024-1920x1080-EN.mp4
03:31:44.936 INFO  Player | moved a video file: SeniorMondays-1920x1080-EN-SR.mp4.TEMP -> SeniorMondays-1920x1080-EN-SR.mp4
03:31:44.936 INFO  Player | moved a video file: WOK-CNY2025-1920x1080-FR_SR.mp4.TEMP -> WOK-CNY2025-1920x1080-FR_SR.mp4
03:31:44.969 INFO  Player | loadContents: 9
03:31:44.972 INFO  Player | loadContainers: 7
03:31:44.972 INFO  Player | Try to delete non existing file: resources\csv\inventory_definition.csv
03:31:44.973 DEBUG Player | updateInventoryItemsWithNewData 0 / 0
03:31:44.973 INFO  Player | Try to delete non existing file: resources\csv\inventory.csv
03:31:44.973 INFO  Player | createInventoryRecords: 0 / 0
03:31:44.974 INFO  Player | Get null feature definition
03:31:44.974 INFO  Player | saveObjectToXMLFile: resources\xml\inventory_def.xml
03:31:44.977 INFO  Player | saveObjectToXMLFile: resources\xml\inventory.xml
03:31:44.984 INFO  Player | clean up containerviews
03:31:44.985 INFO  Player | get box size: 0, 0
03:31:44.985 INFO  Player | get max size from containers: 9600, 1080
03:31:44.985 INFO  Player | saveObjectToXMLFile: resources\xml\preferences.xml
03:31:44.992 INFO  Player | setupBoxes add A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:44.995 INFO  Player | setupBoxes add FS(363380): (0,0,9600,1080), depth: 1, type: All
03:31:44.996 INFO  Player | setupBoxes add C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:45.046 INFO  Player | setupBoxes add B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.048 INFO  Player | setupBoxes add S(363379): (0,0,9600,1080), depth: 2, type: All
03:31:45.080 INFO  Player | setupBoxes add E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:45.081 INFO  Player | setupBoxes add D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.102 DEBUG Player | PageLayout Updated
03:31:45.103 INFO  Player | Now Time: Fri Jul 11 03:31:45 EDT 2025. BoxID: 19352
03:31:45.134 INFO  Player | Record(1811271) [2025-07-09 00:00:00 ~ 2030-01-01 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.136 INFO  Player | Record(1811339) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:5, clear:false, trans:-1 VIDEO(2133916): https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:45.163 INFO  Player | Record(1811272) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:45.166 INFO  Player | Record(1811275) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2133589): https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.172 INFO  Player | Record(1811276) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.172 INFO  PlayerUpdateConfirmation | [Fri Jul 11 03:31:45 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=ContentUpdateConfirmation&jsonparameter={}
03:31:45.172 INFO  Player | Record(1811277) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2133784): https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.174 INFO  Player | Record(1811278) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.178 INFO  Player | Record(1811280) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.178 INFO  Player | Record(1811281) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2133549): https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-EN-1920x1080.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.181 INFO  Player | Record(1811282) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.182 INFO  Player | Record(1811340) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:5, clear:false, trans:-1 IMAGE(2133914): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg @ E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:45.182 INFO  Player | Record(1811284) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133914): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.183 INFO  Player | Record(1811285) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133914): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:45.184 INFO  Player | Record(1811283) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2133589): https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.185 INFO  Player | Record(1811286) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.186 INFO  Player | Record(1811338) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:5, clear:false, trans:-1 VIDEO(2133916): https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.188 INFO  Player | Record(1811287) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2133784): https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.188 INFO  Player | Record(1811342) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:40, clear:false, trans:-1 VIDEO(2133918): https://images.unoapp.com/boxdata/asset89534/images/MicrosoftTeams-video.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:45.189 INFO  Player | Record(1811288) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133914): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.189 INFO  Player | Record(1811295) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2130547): https://images.unoapp.com/boxdata/asset88393/images/MW-HolidayGiftCard2024-1920x1080-EN.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.190 INFO  Player | Record(1811290) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133914): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:45.190 INFO  Player | Record(1811337) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:5, clear:false, trans:-1 VIDEO(2133916): https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:45.190 INFO  Player | Record(1811292) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:45.191 INFO  Player | Record(1811293) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 VIDEO(2131620): https://images.unoapp.com/boxdata/asset89122/images/WOK-CNY2025-1920x1080-FR_SR.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.191 INFO  Player | Record(1811294) [2025-07-09 00:00:00 ~ 2070-12-31 23:59:59 Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday,] pause:0, clear:false, trans:-1 IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:45.191 INFO  Player | Total number of record: 25, playlist size: 1, isFirstTime: true
03:31:45.192 INFO  Player | Playing record[1811271]: true. IMAGE(2133913): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:45.222 INFO  PlayerUpdateConfirmation | POST: 200OK Json parameter is null
03:31:45.248 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:45.249 DEBUG Player | Show ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:45.250 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:45.258 INFO  Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on A
03:31:45.259 DEBUG Player | Playing pause time111: 0
03:31:45.260 DEBUG Player | Playing pause time: 200
03:31:45.461 INFO  Player | Playing record[1811339]: true. VIDEO(2133916): https://images.unoapp.com/boxdata/asset89534/images/f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All; pause:5, clear:false, trans:-1
03:31:45.461 DEBUG Player | aaaa:2133916  363385
03:31:45.505 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133916, 0, 5400
03:31:45.506 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:45.651 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:46.076 INFO  AWT-EventQueue-0 | Video get duration: 5042, delay: 50
03:31:46.084 DEBUG Player | Show ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:46.084 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]}
03:31:46.086 INFO  Player | setContent resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 on E
03:31:46.087 DEBUG Player | Playing pause time111: 5000
03:31:46.090 DEBUG Player | Playing pause time: 200
03:31:46.291 INFO  Player | Playing record[1811272]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All; pause:0, clear:false, trans:-1
03:31:46.291 INFO  Player | Find shared content: IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:46.296 DEBUG Player | Copy file for IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:46.328 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:46.330 DEBUG Player | Show ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:46.331 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]}
03:31:46.358 INFO  Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on C
03:31:46.361 DEBUG Player | Playing pause time111: 0
03:31:46.382 DEBUG Player | Playing pause time: 200
03:31:46.585 INFO  Player | Playing record[1811275]: true. VIDEO(2133589): https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-FR-1920x1080.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:31:46.585 DEBUG Player | aaaa:2133589  363382
03:31:46.588 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133589, 0, 5400
03:31:46.592 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:31:46.637 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:31:46.980 INFO  AWT-EventQueue-0 | Video get duration: 10000, delay: 50
03:31:46.986 DEBUG Player | Show ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:31:46.988 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]}
03:31:46.990 INFO  Player | setContent resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 on B
03:31:46.991 DEBUG Player | Playing pause time111: 0
03:31:46.992 DEBUG Player | Playing pause time: 200
03:31:47.194 INFO  Player | Playing record[1811276]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:31:47.194 DEBUG Player | Close the video in order to play new content
03:31:47.197 INFO  Player | Find shared content: IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:47.200 DEBUG Player | Copy file for IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:47.229 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:47.232 DEBUG Player | Show ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:47.232 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]}
03:31:47.235 DEBUG Player | Hide ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:31:47.239 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on B
03:31:47.239 DEBUG Player | Playing pause time111: 0
03:31:47.242 DEBUG Player | Playing pause time: 200
03:31:47.398 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:31:47.444 INFO  Player | Playing record[1811277]: true. VIDEO(2133784): https://images.unoapp.com/boxdata/asset89122/images/SeniorMondays-1920x1080-EN-SR.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:31:47.444 DEBUG Player | aaaa:2133784  363384
03:31:47.451 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133784, 0, 5400
03:31:47.451 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:31:47.497 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:31:47.967 INFO  AWT-EventQueue-0 | Video get duration: 15000, delay: 50
03:31:47.972 DEBUG Player | Show ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:31:47.973 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]}
03:31:47.973 INFO  Player | setContent resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 on D
03:31:47.974 DEBUG Player | Playing pause time111: 0
03:31:47.975 DEBUG Player | Playing pause time: 200
03:31:48.176 INFO  Player | Playing record[1811278]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:31:48.176 DEBUG Player | Show ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:48.178 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]}
03:31:48.178 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on B
03:31:48.184 DEBUG Player | Playing pause time111: 0
03:31:48.184 DEBUG Player | Playing pause time: 200
03:31:48.391 INFO  Player | Playing record[1811280]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:31:48.391 DEBUG Player | Close the video in order to play new content
03:31:48.394 INFO  Player | Find shared content: IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:48.398 DEBUG Player | Copy file for IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:48.429 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:48.431 DEBUG Player | Show ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:48.434 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:48.434 DEBUG Player | Hide ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:31:48.436 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on D
03:31:48.436 DEBUG Player | Playing pause time111: 0
03:31:48.446 DEBUG Player | Playing pause time: 200
03:31:48.596 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:31:48.724 INFO  Player | Playing record[1811281]: true. VIDEO(2133549): https://images.unoapp.com/boxdata/asset89122/images/LTO-FTM-JULY2025-EN-1920x1080.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:48.724 DEBUG Player | aaaa:2133549  363381
03:31:48.726 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133549, 0, 5400
03:31:48.727 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080]
03:31:48.759 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4
03:31:49.213 INFO  AWT-EventQueue-0 | Video get duration: 10000, delay: 50
03:31:49.218 DEBUG Player | Show ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080]
03:31:49.221 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:49.222 DEBUG Player | Hide ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:49.222 DEBUG Player | setContent resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4 on A
03:31:49.229 DEBUG Player | Playing pause time111: 0
03:31:49.234 DEBUG Player | Playing pause time: 200
03:31:49.434 INFO  Player | Playing record[1811282]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:49.434 DEBUG Player | Close the video in order to play new content
03:31:49.436 DEBUG Player | Show ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:49.437 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:49.438 DEBUG Player | Hide ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080]
03:31:49.443 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on A
03:31:49.443 DEBUG Player | Playing pause time111: 0
03:31:49.448 DEBUG Player | Playing pause time: 200
03:31:49.639 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4
03:31:49.655 INFO  Player | Playing record[1811340]: true. IMAGE(2133914): https://images.unoapp.com/boxdata/asset89534/images/pexels-souvenirpixel..._imresizer(1).jpeg @ E(363385): (0,0,1920,1080), depth: 3, type: All; pause:5, clear:false, trans:-1
03:31:49.656 DEBUG Player | Close the video in order to play new content
03:31:49.688 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:49.691 DEBUG Player | Show ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:49.691 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:49.693 DEBUG Player | Hide ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:49.702 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on E
03:31:49.703 DEBUG Player | Playing pause time111: 5000
03:31:49.704 DEBUG Player | Playing pause time: 200
03:31:49.857 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:49.904 INFO  Player | Playing record[1811284]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:49.905 INFO  Player | Find shared content: IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:49.911 DEBUG Player | Copy file for IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:49.941 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:49.942 DEBUG Player | Show ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:49.942 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:49.944 DEBUG Player | Hide ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:49.945 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on A
03:31:49.946 DEBUG Player | Playing pause time111: 0
03:31:49.946 DEBUG Player | Playing pause time: 200
03:31:50.159 INFO  Player | Playing record[1811285]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All; pause:0, clear:false, trans:-1
03:31:50.159 INFO  Player | Find shared content: IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:50.163 DEBUG Player | Copy file for IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:50.220 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:50.221 DEBUG Player | Show ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:50.222 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:50.224 DEBUG Player | Hide ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:50.227 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on C
03:31:50.228 DEBUG Player | Playing pause time111: 0
03:31:50.228 DEBUG Player | Playing pause time: 200
03:31:50.429 INFO  Player | Playing record[1811283]: true. VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:31:50.429 DEBUG Player | aaaa:2133589  363384
03:31:50.430 INFO  Player | Find shared content: VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:50.435 DEBUG Player | Copy file for VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:50.435 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133589, 0, 5400
03:31:50.435 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:31:50.468 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:31:50.958 INFO  AWT-EventQueue-0 | Video get duration: 10000, delay: 50
03:31:50.961 DEBUG Player | Show ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:31:50.962 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]}
03:31:50.963 DEBUG Player | Hide ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:50.963 DEBUG Player | setContent resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 on D
03:31:50.963 DEBUG Player | Playing pause time111: 0
03:31:50.964 DEBUG Player | Playing pause time: 200
03:31:51.164 INFO  Player | Playing record[1811286]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:31:51.164 DEBUG Player | Close the video in order to play new content
03:31:51.166 DEBUG Player | Show ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:51.166 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:51.181 DEBUG Player | Hide ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:31:51.195 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on D
03:31:51.195 DEBUG Player | Playing pause time111: 0
03:31:51.211 DEBUG Player | Playing pause time: 200
03:31:51.367 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:31:51.414 INFO  Player | Playing record[1811338]: true. VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:5, clear:false, trans:-1
03:31:51.414 DEBUG Player | aaaa:2133916  363381
03:31:51.416 INFO  Player | Find shared content: VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:51.425 DEBUG Player | Copy file for VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:51.428 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133916, 0, 5400
03:31:51.428 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:51.457 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:51.974 INFO  AWT-EventQueue-0 | Video get duration: 5042, delay: 50
03:31:51.977 DEBUG Player | Show ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:51.978 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:51.980 DEBUG Player | Hide ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:51.980 DEBUG Player | setContent resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 on A
03:31:51.982 DEBUG Player | Playing pause time111: 5000
03:31:51.982 DEBUG Player | Playing pause time: 200
03:31:52.183 INFO  Player | Playing record[1811287]: true. VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:31:52.183 DEBUG Player | aaaa:2133784  363382
03:31:52.185 INFO  Player | Find shared content: VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All
03:31:52.190 DEBUG Player | Copy file for VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:52.191 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133784, 0, 5400
03:31:52.191 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:31:52.225 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:31:52.793 INFO  AWT-EventQueue-0 | Video get duration: 15000, delay: 50
03:31:52.797 DEBUG Player | Show ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:31:52.798 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:52.798 DEBUG Player | Hide ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:52.799 DEBUG Player | setContent resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 on B
03:31:52.799 DEBUG Player | Playing pause time111: 0
03:31:52.799 DEBUG Player | Playing pause time: 200
03:31:53.000 INFO  Player | Playing record[1811342]: true. VIDEO(2133918): https://images.unoapp.com/boxdata/asset89534/images/MicrosoftTeams-video.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All; pause:40, clear:false, trans:-1
03:31:53.000 DEBUG Player | aaaa:2133918  363385
03:31:53.002 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133918, 0, 5400
03:31:53.003 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080]
03:31:53.039 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\MicrosoftTeams-video.mp4
03:31:53.685 INFO  AWT-EventQueue-0 | Video get duration: 53133, delay: 50
03:31:53.690 DEBUG Player | Show ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080]
03:31:53.692 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:53.693 DEBUG Player | Hide ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:53.694 DEBUG Player | setContent resources\videos\MicrosoftTeams-video.mp4 on E
03:31:53.695 DEBUG Player | Playing pause time111: 40000
03:31:53.696 DEBUG Player | Playing pause time: 200
03:31:53.902 INFO  Player | Playing record[1811288]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:31:53.903 DEBUG Player | Close the video in order to play new content
03:31:53.908 INFO  Player | Find shared content: IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All
03:31:53.911 DEBUG Player | Copy file for IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All
03:31:53.939 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:53.941 DEBUG Player | Show ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:53.941 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:53.943 DEBUG Player | Hide ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:31:53.953 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on B
03:31:53.954 DEBUG Player | Playing pause time111: 0
03:31:53.962 DEBUG Player | Playing pause time: 200
03:31:54.111 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:31:54.165 INFO  Player | Playing record[1811295]: true. VIDEO(2130547): https://images.unoapp.com/boxdata/asset88393/images/MW-HolidayGiftCard2024-1920x1080-EN.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:31:54.165 DEBUG Player | aaaa:2130547  363384
03:31:54.167 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2130547, 0, 5400
03:31:54.167 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]
03:31:54.205 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4
03:31:54.957 INFO  AWT-EventQueue-0 | Video get duration: 12000, delay: 50
03:31:54.962 DEBUG Player | Show ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]
03:31:54.964 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]}
03:31:54.965 DEBUG Player | Hide ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:54.970 DEBUG Player | setContent resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4 on D
03:31:54.973 DEBUG Player | Playing pause time111: 0
03:31:54.973 DEBUG Player | Playing pause time: 200
03:31:55.179 INFO  Player | Playing record[1811290]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:31:55.191 DEBUG Player | Show ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:55.192 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]}
03:31:55.224 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on B
03:31:55.228 DEBUG Player | Playing pause time111: 0
03:31:55.232 DEBUG Player | Playing pause time: 200
03:31:55.433 INFO  Player | Playing record[1811337]: true. VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ C(363383): (3840,0,1920,1080), depth: 5, type: All; pause:5, clear:false, trans:-1
03:31:55.433 DEBUG Player | aaaa:2133916  363383
03:31:55.434 INFO  Player | Find shared content: VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All
03:31:55.438 DEBUG Player | Copy file for VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ C(363383): (3840,0,1920,1080), depth: 5, type: All
03:31:55.439 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2133916, 0, 5400
03:31:55.439 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:55.488 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:56.131 INFO  AWT-EventQueue-0 | Video get duration: 5042, delay: 50
03:31:56.136 DEBUG Player | Show ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:56.152 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]}
03:31:56.154 DEBUG Player | Hide ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:31:56.155 DEBUG Player | setContent resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 on C
03:31:56.164 DEBUG Player | Playing pause time111: 5000
03:31:56.168 DEBUG Player | Playing pause time: 200
03:31:56.370 INFO  Player | Playing record[1811292]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:31:56.370 DEBUG Player | Close the video in order to play new content
03:31:56.378 DEBUG Player | Show ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:56.379 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:56.379 DEBUG Player | Hide ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]
03:31:56.387 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on D
03:31:56.401 DEBUG Player | Playing pause time111: 0
03:31:56.403 DEBUG Player | Playing pause time: 200
03:31:56.578 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4
03:31:56.663 INFO  Player | Playing record[1811293]: true. VIDEO(2131620): https://images.unoapp.com/boxdata/asset89122/images/WOK-CNY2025-1920x1080-FR_SR.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:56.664 DEBUG Player | aaaa:2131620  363381
03:31:56.666 DEBUG Player | Close the video in order to play new content
03:31:56.668 INFO  AWT-EventQueue-0 | new WMPVideoBox for CID 2131620, 0, 5400
03:31:56.677 DEBUG AWT-EventQueue-0 | updateContainer add to views: ContentBox [uid=213525381, content=VIDEO(2131620): resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4, width=1920, height=1080]
03:31:56.716 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4
03:31:57.531 INFO  AWT-EventQueue-0 | Video get duration: 12000, delay: 50
03:31:57.537 DEBUG Player | Show ContentBox [uid=213525381, content=VIDEO(2131620): resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4, width=1920, height=1080]
03:31:57.538 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:57.539 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213525381, content=VIDEO(2131620): resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:57.542 DEBUG Player | Hide ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:57.544 DEBUG Player | setContent resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4 on A
03:31:57.544 DEBUG Player | Playing pause time111: 0
03:31:57.545 DEBUG Player | Playing pause time: 200
03:31:57.749 INFO  Player | Playing record[1811294]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:57.750 DEBUG Player | Close the video in order to play new content
03:31:57.755 DEBUG Player | Show ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:57.772 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:57.772 DEBUG Player | Hide ContentBox [uid=213525381, content=VIDEO(2131620): resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4, width=1920, height=1080]
03:31:57.823 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on A
03:31:57.829 DEBUG Player | Playing pause time111: 0
03:31:57.830 DEBUG Player | Playing pause time: 200
03:31:57.968 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\WOK-CNY2025-1920x1080-FR_SR.mp4
03:31:58.044 INFO  Player | Total number of record: 25, playlist size: 1, isFirstTime: false
03:31:58.048 INFO  Player | Playing record[1811271]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:31:58.049 DEBUG Player | Show ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:31:58.064 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:58.065 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on A
03:31:58.066 DEBUG Player | Playing pause time111: 0
03:31:58.067 DEBUG Player | Playing pause time: 1
03:31:58.069 INFO  Player | Playing record[1811339]: true. VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All; pause:5, clear:false, trans:-1
03:31:58.069 DEBUG Player | aaaa:2133916  363385
03:31:58.069 DEBUG Player | aaaa:5042
03:31:58.070 DEBUG Player | Close the video in order to play new content
03:31:58.072 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:31:58.073 DEBUG Player | Show ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:31:58.095 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:31:58.137 DEBUG Player | Hide ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080]
03:31:58.140 DEBUG Player | setContent resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 on E
03:31:58.182 DEBUG Player | Playing pause time111: 5000
03:31:58.196 DEBUG Player | Playing pause time: 5000
03:31:58.272 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\MicrosoftTeams-video.mp4
03:32:03.202 INFO  Player | Playing record[1811272]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All; pause:0, clear:false, trans:-1
03:32:03.203 DEBUG Player | Close the video in order to play new content
03:32:03.206 DEBUG Player | Show ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:03.210 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:03.210 DEBUG Player | Hide ContentBox [uid=213754983, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:32:03.216 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on C
03:32:03.217 DEBUG Player | Playing pause time111: 0
03:32:03.222 DEBUG Player | Playing pause time: 1
03:32:03.223 INFO  Player | Playing record[1811275]: true. VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:32:03.232 DEBUG Player | aaaa:2133589  363382
03:32:03.234 DEBUG Player | aaaa:10000
03:32:03.266 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:32:03.269 DEBUG Player | Show ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:32:03.308 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:03.308 DEBUG Player | Hide ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:32:03.346 DEBUG Player | setContent resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 on B
03:32:03.351 DEBUG Player | Playing pause time111: 10000
03:32:03.375 DEBUG Player | Playing pause time: 10000
03:32:03.408 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:32:11.174 INFO  Timer-1 | Preference Window automatically closed
03:32:13.387 INFO  Player | Playing record[1811276]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:32:13.387 DEBUG Player | Close the video in order to play new content
03:32:13.391 DEBUG Player | Show ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:13.392 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:13.392 DEBUG Player | Hide ContentBox [uid=213722282, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:32:13.402 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on B
03:32:13.403 DEBUG Player | Playing pause time111: 0
03:32:13.404 DEBUG Player | Playing pause time: 1
03:32:13.421 INFO  Player | Playing record[1811277]: true. VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:32:13.459 DEBUG Player | aaaa:2133784  363384
03:32:13.464 DEBUG Player | aaaa:15000
03:32:13.488 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:32:13.550 DEBUG Player | Show ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:32:13.553 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]}
03:32:13.553 DEBUG Player | Hide ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:13.554 DEBUG Player | setContent resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 on D
03:32:13.592 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:32:13.607 DEBUG Player | Playing pause time111: 15000
03:32:13.632 DEBUG Player | Playing pause time: 15000
03:32:17.529 INFO  Updater | [Fri Jul 11 03:32:17 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:32:17.594 INFO  Updater | POST: 200OK false
03:32:19.856 INFO  Monitor | Mem KB(max:free): 506816 : 416139
03:32:28.652 INFO  Player | Playing record[1811278]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:32:28.652 DEBUG Player | Show ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:28.654 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]}
03:32:28.661 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on B
03:32:28.662 DEBUG Player | Playing pause time111: 0
03:32:28.663 DEBUG Player | Playing pause time: 1
03:32:28.666 INFO  Player | Playing record[1811280]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:32:28.666 DEBUG Player | Close the video in order to play new content
03:32:28.667 DEBUG Player | Show ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:28.667 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:28.668 DEBUG Player | Hide ContentBox [uid=213741784, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:32:28.682 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on D
03:32:28.683 DEBUG Player | Playing pause time111: 0
03:32:28.684 DEBUG Player | Playing pause time: 1
03:32:28.716 INFO  Player | Playing record[1811281]: true. VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:32:28.716 DEBUG Player | aaaa:2133549  363381
03:32:28.716 DEBUG Player | aaaa:10000
03:32:28.732 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4
03:32:28.776 DEBUG Player | Show ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080]
03:32:28.778 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:28.779 DEBUG Player | Hide ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:28.780 DEBUG Player | setContent resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4 on A
03:32:28.787 DEBUG Player | Playing pause time111: 10000
03:32:28.789 DEBUG Player | Playing pause time: 10000
03:32:28.868 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:32:38.805 INFO  Player | Playing record[1811282]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:32:38.806 DEBUG Player | Close the video in order to play new content
03:32:38.818 DEBUG Player | Show ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:38.819 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:38.827 DEBUG Player | Hide ContentBox [uid=213718281, content=VIDEO(2133549): resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4, width=1920, height=1080]
03:32:38.837 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on A
03:32:38.862 DEBUG Player | Playing pause time111: 0
03:32:38.863 DEBUG Player | Playing pause time: 1
03:32:38.881 INFO  Player | Playing record[1811340]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ E(363385): (0,0,1920,1080), depth: 3, type: All; pause:5, clear:false, trans:-1
03:32:38.889 DEBUG Player | Close the video in order to play new content
03:32:38.892 DEBUG Player | Show ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:32:38.893 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:38.911 DEBUG Player | Hide ContentBox [uid=213754985, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:32:38.932 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on E
03:32:38.959 DEBUG Player | Playing pause time111: 5000
03:32:38.960 DEBUG Player | Playing pause time: 4930
03:32:39.018 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\LTO-FTM-JULY2025-EN-1920x1080.mp4
03:32:39.091 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:32:43.898 INFO  Player | Playing record[1811284]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:0, clear:false, trans:-1
03:32:43.899 DEBUG Player | Show ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:32:43.905 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:43.910 DEBUG Player | Hide ContentBox [uid=213754681, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:43.913 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on A
03:32:43.922 DEBUG Player | Playing pause time111: 0
03:32:43.923 DEBUG Player | Playing pause time: 1
03:32:43.947 INFO  Player | Playing record[1811285]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ C(363383): (3840,0,1920,1080), depth: 5, type: All; pause:0, clear:false, trans:-1
03:32:43.949 DEBUG Player | Show ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:32:43.973 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:43.974 DEBUG Player | Hide ContentBox [uid=213754683, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:44.017 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on C
03:32:44.019 DEBUG Player | Playing pause time111: 0
03:32:44.021 DEBUG Player | Playing pause time: 1
03:32:44.024 INFO  Player | Playing record[1811283]: true. VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:32:44.048 DEBUG Player | aaaa:2133589  363384
03:32:44.050 DEBUG Player | aaaa:10000
03:32:44.055 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:32:44.056 DEBUG Player | Show ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:32:44.092 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]}
03:32:44.092 DEBUG Player | Hide ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:44.135 DEBUG Player | setContent resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4 on D
03:32:44.137 DEBUG Player | Playing pause time111: 10000
03:32:44.162 DEBUG Player | Playing pause time: 10000
03:32:53.775 INFO  Updater | [Fri Jul 11 03:32:53 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:32:53.832 INFO  Updater | POST: 200OK false
03:32:54.174 INFO  Player | Playing record[1811286]: true. IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:32:54.174 DEBUG Player | Close the video in order to play new content
03:32:54.176 DEBUG Player | Show ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:54.179 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:54.180 DEBUG Player | Hide ContentBox [uid=213722284, content=VIDEO(2133589): resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4, width=1920, height=1080]
03:32:54.184 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer.jpeg on D
03:32:54.186 DEBUG Player | Playing pause time111: 0
03:32:54.190 DEBUG Player | Playing pause time: 1
03:32:54.191 INFO  Player | Playing record[1811338]: true. VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ A(363381): (7680,0,1920,1080), depth: 7, type: All; pause:5, clear:false, trans:-1
03:32:54.194 DEBUG Player | aaaa:2133916  363381
03:32:54.195 DEBUG Player | aaaa:5042
03:32:54.215 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4
03:32:54.221 DEBUG Player | Show ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080]
03:32:54.253 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:54.253 DEBUG Player | Hide ContentBox [uid=213754781, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:32:54.268 DEBUG Player | setContent resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 on A
03:32:54.271 DEBUG Player | Playing pause time111: 5000
03:32:54.278 DEBUG Player | Playing pause time: 5000
03:32:54.378 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\LTO-FTM-JULY2025-FR-1920x1080.mp4
03:32:59.283 INFO  Player | Playing record[1811287]: true. VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:32:59.283 DEBUG Player | aaaa:2133784  363382
03:32:59.285 DEBUG Player | aaaa:15000
03:32:59.288 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:32:59.288 DEBUG Player | Show ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:32:59.298 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080], 363385=ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:32:59.299 DEBUG Player | Hide ContentBox [uid=213754682, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:32:59.299 DEBUG Player | setContent resources\videos\SeniorMondays-1920x1080-EN-SR.mp4 on B
03:32:59.300 DEBUG Player | Playing pause time111: 15000
03:32:59.317 DEBUG Player | Playing pause time: 15000
03:33:14.332 INFO  Player | Playing record[1811342]: true. VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4 @ E(363385): (0,0,1920,1080), depth: 3, type: All; pause:40, clear:false, trans:-1
03:33:14.332 DEBUG Player | aaaa:2133918  363385
03:33:14.334 DEBUG Player | aaaa:53133
03:33:14.336 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\MicrosoftTeams-video.mp4
03:33:14.337 DEBUG Player | Show ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080]
03:33:14.350 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:33:14.352 DEBUG Player | Hide ContentBox [uid=213754785, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:33:14.364 DEBUG Player | setContent resources\videos\MicrosoftTeams-video.mp4 on E
03:33:14.368 DEBUG Player | Playing pause time111: 40000
03:33:14.376 DEBUG Player | Playing pause time: 40000
03:33:30.010 INFO  Updater | [Fri Jul 11 03:33:30 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:33:30.070 INFO  Updater | POST: 200OK false
03:33:39.857 INFO  Monitor | Mem KB(max:free): 506816 : 409676
03:33:54.415 INFO  Player | Playing record[1811288]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:33:54.415 DEBUG Player | Close the video in order to play new content
03:33:54.417 DEBUG Player | Show ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:33:54.418 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]}
03:33:54.419 DEBUG Player | Hide ContentBox [uid=213741782, content=VIDEO(2133784): resources\videos\SeniorMondays-1920x1080-EN-SR.mp4, width=1920, height=1080]
03:33:54.424 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on B
03:33:54.424 DEBUG Player | Playing pause time111: 0
03:33:54.425 DEBUG Player | Playing pause time: 1
03:33:54.429 INFO  Player | Playing record[1811295]: true. VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4 @ D(363384): (1920,0,1920,1080), depth: 4, type: All; pause:0, clear:false, trans:-1
03:33:54.450 DEBUG Player | aaaa:2130547  363384
03:33:54.452 DEBUG Player | aaaa:12000
03:33:54.457 INFO  AWT-EventQueue-0 | WMPVideoBox.play: resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4
03:33:54.487 DEBUG Player | Show ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]
03:33:54.491 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]}
03:33:54.492 DEBUG Player | Hide ContentBox [uid=213754684, content=IMAGE(2133913): resources\images\pexels-souvenirpixel..._imresizer.jpeg, width=1920, height=1080]
03:33:54.493 DEBUG Player | setContent resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4 on D
03:33:54.500 DEBUG Player | Playing pause time111: 12000
03:33:54.502 DEBUG Player | Playing pause time: 12000
03:33:54.618 DEBUG AWT-EventQueue-0 | WMPVideoBox stop: resources\videos\SeniorMondays-1920x1080-EN-SR.mp4
03:34:06.252 INFO  Updater | [Fri Jul 11 03:34:06 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:34:06.520 INFO  Player | Playing record[1811290]: true. IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg @ B(363382): (5760,0,1920,1080), depth: 6, type: All; pause:0, clear:false, trans:-1
03:34:06.521 DEBUG Player | Show ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080]
03:34:06.522 INFO  Player | Map Values of containerIDToContentBox : {363381=ContentBox [uid=213754981, content=VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4, width=1920, height=1080], 363383=ContentBox [uid=213754783, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363382=ContentBox [uid=213754782, content=IMAGE(2133914): resources\images\pexels-souvenirpixel..._imresizer(1).jpeg, width=1920, height=1080], 363385=ContentBox [uid=213755185, content=VIDEO(2133918): resources\videos\MicrosoftTeams-video.mp4, width=1920, height=1080], 363384=ContentBox [uid=213418084, content=VIDEO(2130547): resources\videos\MW-HolidayGiftCard2024-1920x1080-EN.mp4, width=1920, height=1080]}
03:34:06.528 DEBUG Player | setContent resources\images\pexels-souvenirpixel..._imresizer(1).jpeg on B
03:34:06.529 DEBUG Player | Playing pause time111: 0
03:34:06.530 DEBUG Player | Playing pause time: 1
03:34:06.533 INFO  Player | Playing record[1811337]: true. VIDEO(2133916): resources\videos\f08e5e77-9db2-4b83-a1d4-05f680312451.mp4 @ C(363383): (3840,0,1920,1080), depth: 5, type: All; pause:5, clear:false, trans:-1
03:34:06.533 DEBUG Player | aaaa:2133916  363383
03:34:06.533 DEBUG Player | aaaa:5042
03:34:06.572 INFO  Updater | POST: 200OK false
03:34:42.749 INFO  Updater | [Fri Jul 11 03:34:42 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:34:42.854 INFO  Updater | POST: 200OK false
03:34:59.858 INFO  Monitor | Mem KB(max:free): 506816 : 406884
03:35:19.031 INFO  Updater | [Fri Jul 11 03:35:19 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:35:19.091 INFO  Updater | POST: 200OK false
03:35:55.273 INFO  Updater | [Fri Jul 11 03:35:55 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:35:55.328 INFO  Updater | POST: 200OK false
03:36:19.858 INFO  Monitor | Mem KB(max:free): 506816 : 404911
03:36:31.506 INFO  Updater | [Fri Jul 11 03:36:31 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:36:31.567 INFO  Updater | POST: 200OK false
03:37:07.748 INFO  Updater | [Fri Jul 11 03:37:07 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:37:07.803 INFO  Updater | POST: 200OK false
03:37:39.859 INFO  Monitor | Mem KB(max:free): 506816 : 403167
03:37:43.983 INFO  Updater | [Fri Jul 11 03:37:43 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:37:44.043 INFO  Updater | POST: 200OK false
03:38:20.214 INFO  Updater | [Fri Jul 11 03:38:20 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:38:20.272 INFO  Updater | POST: 200OK false
03:38:56.458 INFO  Updater | [Fri Jul 11 03:38:56 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:38:56.516 INFO  Updater | POST: 200OK false
03:38:59.860 INFO  Monitor | Mem KB(max:free): 506816 : 401074
03:39:32.694 INFO  Updater | [Fri Jul 11 03:39:32 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:39:32.755 INFO  Updater | POST: 200OK false
03:40:08.934 INFO  Updater | [Fri Jul 11 03:40:08 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:40:09.014 INFO  Updater | POST: 200OK false
03:40:19.860 INFO  Monitor | Mem KB(max:free): 506816 : 399330
03:40:45.189 INFO  Updater | [Fri Jul 11 03:40:45 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:40:45.253 INFO  Updater | POST: 200OK false
03:41:21.429 INFO  Updater | [Fri Jul 11 03:41:21 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:41:21.484 INFO  Updater | POST: 200OK false
03:41:39.861 INFO  Monitor | Mem KB(max:free): 506816 : 397357
03:41:57.657 INFO  Updater | [Fri Jul 11 03:41:57 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:41:57.717 INFO  Updater | POST: 200OK false
03:42:33.892 INFO  Updater | [Fri Jul 11 03:42:33 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:42:33.948 INFO  Updater | POST: 200OK false
03:42:59.861 INFO  Monitor | Mem KB(max:free): 506816 : 395614
03:43:10.122 INFO  Updater | [Fri Jul 11 03:43:10 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:43:10.181 INFO  Updater | POST: 200OK false
03:43:46.357 INFO  Updater | [Fri Jul 11 03:43:46 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:43:46.422 INFO  Updater | POST: 200OK false
03:44:19.862 INFO  Monitor | Mem KB(max:free): 506816 : 393870
03:44:22.599 INFO  Updater | [Fri Jul 11 03:44:22 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:44:22.659 INFO  Updater | POST: 200OK false
03:44:58.835 INFO  Updater | [Fri Jul 11 03:44:58 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:44:58.896 INFO  Updater | POST: 200OK false
03:45:35.079 INFO  Updater | [Fri Jul 11 03:45:35 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:45:35.158 INFO  Updater | POST: 200OK false
03:45:39.863 INFO  Monitor | Mem KB(max:free): 506816 : 390724
03:46:11.336 INFO  Updater | [Fri Jul 11 03:46:11 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:46:11.416 INFO  Updater | POST: 200OK false
03:46:47.599 INFO  Updater | [Fri Jul 11 03:46:47 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:46:47.658 INFO  Updater | POST: 200OK false
03:46:59.863 INFO  Monitor | Mem KB(max:free): 506816 : 388751
03:47:23.826 INFO  Updater | [Fri Jul 11 03:47:23 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:47:23.885 INFO  Updater | POST: 200OK false
03:48:00.069 INFO  Updater | [Fri Jul 11 03:48:00 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:48:00.131 INFO  Updater | POST: 200OK false
03:48:19.864 INFO  Monitor | Mem KB(max:free): 506816 : 387007
03:48:36.311 INFO  Updater | [Fri Jul 11 03:48:36 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:48:36.369 INFO  Updater | POST: 200OK false
03:49:12.549 INFO  Updater | [Fri Jul 11 03:49:12 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:49:12.612 INFO  Updater | POST: 200OK false
03:49:39.865 INFO  Monitor | Mem KB(max:free): 506816 : 384154
03:49:48.791 INFO  Updater | [Fri Jul 11 03:49:48 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:49:48.851 INFO  Updater | POST: 200OK false
03:50:25.027 INFO  Updater | [Fri Jul 11 03:50:25 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:50:25.091 INFO  Updater | POST: 200OK false
03:50:59.865 INFO  Monitor | Mem KB(max:free): 506816 : 383456
03:51:01.268 INFO  Updater | [Fri Jul 11 03:51:01 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:51:01.328 INFO  Updater | POST: 200OK false
03:51:37.509 INFO  Updater | [Fri Jul 11 03:51:37 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:51:37.584 INFO  Updater | POST: 200OK false
03:52:13.760 INFO  Updater | [Fri Jul 11 03:52:13 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:52:13.819 INFO  Updater | POST: 200OK false
03:52:19.866 INFO  Monitor | Mem KB(max:free): 506816 : 379880
03:52:49.995 INFO  Updater | [Fri Jul 11 03:52:49 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:52:50.059 INFO  Updater | POST: 200OK false
03:53:26.232 INFO  Updater | [Fri Jul 11 03:53:26 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:53:26.296 INFO  Updater | POST: 200OK false
03:53:39.867 INFO  Monitor | Mem KB(max:free): 506816 : 378137
03:54:02.465 INFO  Updater | [Fri Jul 11 03:54:02 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:54:02.523 INFO  Updater | POST: 200OK false
03:54:38.694 INFO  Updater | [Fri Jul 11 03:54:38 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:54:38.752 INFO  Updater | POST: 200OK false
03:54:59.868 INFO  Monitor | Mem KB(max:free): 506816 : 376377
03:55:14.923 INFO  Updater | [Fri Jul 11 03:55:14 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:55:14.979 INFO  Updater | POST: 200OK false
03:55:51.154 INFO  Updater | [Fri Jul 11 03:55:51 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:55:51.214 INFO  Updater | POST: 200OK false
03:56:19.869 INFO  Monitor | Mem KB(max:free): 506816 : 374404
03:56:27.385 INFO  Updater | [Fri Jul 11 03:56:27 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:56:27.449 INFO  Updater | POST: 200OK false
03:57:03.624 INFO  Updater | [Fri Jul 11 03:57:03 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:57:03.710 INFO  Updater | POST: 200OK false
03:57:39.870 INFO  Monitor | Mem KB(max:free): 506816 : 372660
03:57:39.892 INFO  Updater | [Fri Jul 11 03:57:39 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:57:39.947 INFO  Updater | POST: 200OK false
03:58:16.120 INFO  Updater | [Fri Jul 11 03:58:16 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:58:16.176 INFO  Updater | POST: 200OK false
03:58:52.354 INFO  Updater | [Fri Jul 11 03:58:52 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:58:52.411 INFO  Updater | POST: 200OK false
03:58:59.870 INFO  Monitor | Mem KB(max:free): 506816 : 370567
03:59:28.583 INFO  Updater | [Fri Jul 11 03:59:28 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
03:59:28.642 INFO  Updater | POST: 200OK false
04:00:04.644 INFO  Updater | Updater thread is running..
04:00:04.819 INFO  Updater | [Fri Jul 11 04:00:04 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:00:04.877 INFO  Updater | POST: 200OK false
04:00:19.871 INFO  Monitor | Mem KB(max:free): 506816 : 368824
04:00:40.879 INFO  Updater | Updater thread is running..
04:00:41.051 INFO  Updater | [Fri Jul 11 04:00:41 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:00:41.115 INFO  Updater | POST: 200OK false
04:01:17.288 INFO  Updater | [Fri Jul 11 04:01:17 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:01:17.347 INFO  Updater | POST: 200OK false
04:01:39.872 INFO  Monitor | Mem KB(max:free): 506816 : 366850
04:01:53.515 INFO  Updater | [Fri Jul 11 04:01:53 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:01:53.570 INFO  Updater | POST: 200OK false
04:02:29.745 INFO  Updater | [Fri Jul 11 04:02:29 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:02:29.827 INFO  Updater | POST: 200OK false
04:02:59.872 INFO  Monitor | Mem KB(max:free): 506816 : 365099
04:03:06.006 INFO  Updater | [Fri Jul 11 04:03:06 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:03:06.068 INFO  Updater | POST: 200OK false
04:03:42.252 INFO  Updater | [Fri Jul 11 04:03:42 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:03:42.310 INFO  Updater | POST: 200OK false
04:04:18.489 INFO  Updater | [Fri Jul 11 04:04:18 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:04:18.575 INFO  Updater | POST: 200OK false
04:04:19.873 INFO  Monitor | Mem KB(max:free): 506816 : 361961
04:04:54.744 INFO  Updater | [Fri Jul 11 04:04:54 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:04:54.829 INFO  Updater | POST: 200OK false
04:05:31.005 INFO  Updater | [Fri Jul 11 04:05:31 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:05:31.063 INFO  Updater | POST: 200OK false
04:05:39.874 INFO  Monitor | Mem KB(max:free): 506816 : 360201
04:06:07.241 INFO  Updater | [Fri Jul 11 04:06:07 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:06:07.305 INFO  Updater | POST: 200OK false
04:06:43.479 INFO  Updater | [Fri Jul 11 04:06:43 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:06:43.536 INFO  Updater | POST: 200OK false
04:06:59.874 INFO  Monitor | Mem KB(max:free): 506816 : 359273
04:07:19.718 INFO  Updater | [Fri Jul 11 04:07:19 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:07:19.776 INFO  Updater | POST: 200OK false
04:07:55.954 INFO  Updater | [Fri Jul 11 04:07:55 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:07:56.033 INFO  Updater | POST: 200OK false
04:08:19.875 INFO  Monitor | Mem KB(max:free): 506816 : 356460
04:08:32.213 INFO  Updater | [Fri Jul 11 04:08:32 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:08:32.276 INFO  Updater | POST: 200OK false
04:09:08.452 INFO  Updater | [Fri Jul 11 04:09:08 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:09:08.508 INFO  Updater | POST: 200OK false
04:09:39.876 INFO  Monitor | Mem KB(max:free): 506816 : 355761
04:09:44.682 INFO  Updater | [Fri Jul 11 04:09:44 EDT 2025] POST: https://www.digitalmarketingbox.com/webtool/playerupdate.php?menuboxid=19352&requestFrom=unoappplayer&updatetoken=dmb20101512&reqtype=checkupdate
04:09:44.739 INFO  Updater | POST: 200OK false
